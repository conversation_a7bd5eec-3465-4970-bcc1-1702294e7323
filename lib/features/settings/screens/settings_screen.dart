import 'package:fluentui_system_icons/fluentui_system_icons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pixs/features/debug/screens/feature_flags_debug_screen.dart';
import 'package:pixs/shared/app/extension/helper.dart';
import 'package:pixs/shared/constants/base_url.dart';
import 'package:pixs/shared/constants/colors.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ListView(
        children: [
          // ListTile(
          //   title: const Text('Send Feedback'),
          //   subtitle: const Text('Help us improve your experience'),
          //   leading: const Icon(FluentIcons.coin_stack_20_filled),
          //   onTap: () {},
          // ),
          // const _Seperator(),
          // ListTile(
          //   title: const Text('Clear Cache'),
          //   subtitle: const Text('Clear the cache to free up space'),
          //   leading: const Icon(FluentIcons.coin_stack_20_filled),
          //   onTap: () {},
          // ),
          const _Seperator(),
          ListTile(
            title: const Text('Feature Flags Debug'),
            subtitle: const Text('View and test Firebase Remote Config'),
            leading: const Icon(FluentIcons.developer_board_20_filled),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const FeatureFlagsDebugScreen(),
                ),
              );
            },
          ),
          const _Seperator(),
          ListTile(
            title: const Text('Privacy Policy'),
            subtitle: const Text('View Pixs Privacy Policy'),
            leading: const Icon(FluentIcons.shield_20_filled),
            onTap: () => Helper().launchUrls(privacyPolicyUrl),
          ),
          const _Seperator(),
          ListTile(
              title: const Text('App Version'),
              subtitle: const Text('v1.0.0'),
              leading: const Icon(FluentIcons.image_20_filled),
              onTap: () {}),
        ],
      ),
    );
  }
}

class _Seperator extends StatelessWidget {
  const _Seperator({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.0.w),
      child: Divider(
        thickness: 0.5.h,
        color: kBorderColor.withOpacity(0.5),
      ),
    );
  }
}
