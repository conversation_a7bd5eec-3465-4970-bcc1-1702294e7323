import 'dart:developer';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:injectable/injectable.dart';

@lazySingleton
class FirebaseRemoteConfigService {
  static FirebaseRemoteConfig? _remoteConfig;
  
  // Feature flag keys
  static const String _enhancePromptFeatureKey = 'enable_enhance_prompt_feature';
  static const String _generateImageFeatureKey = 'enable_generate_image_feature';

  /// Initialize Firebase Remote Config
  Future<void> initialize() async {
    try {
      _remoteConfig = FirebaseRemoteConfig.instance;

      // Set configuration settings
      await _remoteConfig!.setConfigSettings(
        RemoteConfigSettings(
          fetchTimeout: const Duration(minutes: 1),
          minimumFetchInterval: const Duration(hours: 1),
        ),
      );

      // Set default values with explicit types
      await _remoteConfig!.setDefaults({
        _enhancePromptFeatureKey: true,
        _generateImageFeatureKey: true,
      });

      // Fetch and activate remote config
      await _fetchAndActivate();

      log('Firebase Remote Config initialized successfully');
    } catch (e) {
      log('Error initializing Firebase Remote Config: $e');
      // Don't rethrow - allow app to continue with default values
    }
  }

  /// Fetch and activate remote config values
  Future<bool> _fetchAndActivate() async {
    try {
      final bool fetched = await _remoteConfig!.fetchAndActivate();
      log('Remote Config fetch and activate: $fetched');
      return fetched;
    } catch (e) {
      log('Error fetching remote config: $e');
      return false;
    }
  }

  /// Force fetch remote config (useful for testing)
  Future<bool> forceFetch() async {
    try {
      await _remoteConfig!.fetch();
      await _remoteConfig!.activate();
      log('Remote Config force fetched successfully');
      return true;
    } catch (e) {
      log('Error force fetching remote config: $e');
      return false;
    }
  }

  /// Check if enhance prompt feature is enabled
  bool get isEnhancePromptFeatureEnabled {
    try {
      return _remoteConfig?.getBool(_enhancePromptFeatureKey) ?? true;
    } catch (e) {
      log('Error getting enhance prompt feature flag: $e');
      return true; // Default to enabled
    }
  }

  /// Check if generate image feature is enabled
  bool get isGenerateImageFeatureEnabled {
    try {
      return _remoteConfig?.getBool(_generateImageFeatureKey) ?? true;
    } catch (e) {
      log('Error getting generate image feature flag: $e');
      return true; // Default to enabled
    }
  }



  /// Get all feature flags as a map
  Map<String, bool> get allFeatureFlags {
    return {
      'enhancePromptFeature': isEnhancePromptFeatureEnabled,
      'generateImageFeature': isGenerateImageFeatureEnabled,
    };
  }

  /// Get a custom string value from remote config
  String getString(String key, {String defaultValue = ''}) {
    try {
      return _remoteConfig?.getString(key) ?? defaultValue;
    } catch (e) {
      log('Error getting string value for key $key: $e');
      return defaultValue;
    }
  }

  /// Get a custom boolean value from remote config
  bool getBool(String key, {bool defaultValue = false}) {
    try {
      return _remoteConfig?.getBool(key) ?? defaultValue;
    } catch (e) {
      log('Error getting boolean value for key $key: $e');
      return defaultValue;
    }
  }

  /// Get a custom integer value from remote config
  int getInt(String key, {int defaultValue = 0}) {
    try {
      return _remoteConfig?.getInt(key) ?? defaultValue;
    } catch (e) {
      log('Error getting integer value for key $key: $e');
      return defaultValue;
    }
  }

  /// Get a custom double value from remote config
  double getDouble(String key, {double defaultValue = 0.0}) {
    try {
      return _remoteConfig?.getDouble(key) ?? defaultValue;
    } catch (e) {
      log('Error getting double value for key $key: $e');
      return defaultValue;
    }
  }

  /// Listen to remote config updates
  Stream<RemoteConfigUpdate> get onConfigUpdated {
    return _remoteConfig?.onConfigUpdated ?? const Stream.empty();
  }

  /// Get the last fetch time
  DateTime? get lastFetchTime {
    return _remoteConfig?.lastFetchTime;
  }

  /// Get the last fetch status
  RemoteConfigFetchStatus get lastFetchStatus {
    return _remoteConfig?.lastFetchStatus ?? RemoteConfigFetchStatus.noFetchYet;
  }

  /// Check if remote config is available
  bool get isAvailable {
    return _remoteConfig != null;
  }
}
