import 'dart:developer';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:injectable/injectable.dart';

@lazySingleton
class FirebaseRemoteConfigService {
  static FirebaseRemoteConfig? _remoteConfig;
  
  // Feature flag keys
  static const String _enhancePromptFeatureKey = 'enable_enhance_prompt_feature';
  static const String _generateImageFeatureKey = 'enable_generate_image_feature';
  static const String _imageGenerationTabKey = 'enable_image_generation_tab';
  static const String _promptEnhancementTabKey = 'enable_prompt_enhancement_tab';
  
  // Default values
  static const Map<String, dynamic> _defaultValues = {
    _enhancePromptFeatureKey: true,
    _generateImageFeatureKey: true,
    _imageGenerationTabKey: true,
    _promptEnhancementTabKey: true,
  };

  /// Initialize Firebase Remote Config
  Future<void> initialize() async {
    try {
      _remoteConfig = FirebaseRemoteConfig.instance;
      
      // Set configuration settings
      await _remoteConfig!.setConfigSettings(
        RemoteConfigSettings(
          fetchTimeout: const Duration(minutes: 1),
          minimumFetchInterval: const Duration(hours: 1),
        ),
      );
      
      // Set default values
      await _remoteConfig!.setDefaults(_defaultValues);
      
      // Fetch and activate remote config
      await _fetchAndActivate();
      
      log('Firebase Remote Config initialized successfully');
    } catch (e) {
      log('Error initializing Firebase Remote Config: $e');
    }
  }

  /// Fetch and activate remote config values
  Future<bool> _fetchAndActivate() async {
    try {
      final bool fetched = await _remoteConfig!.fetchAndActivate();
      log('Remote Config fetch and activate: $fetched');
      return fetched;
    } catch (e) {
      log('Error fetching remote config: $e');
      return false;
    }
  }

  /// Force fetch remote config (useful for testing)
  Future<bool> forceFetch() async {
    try {
      await _remoteConfig!.fetch();
      await _remoteConfig!.activate();
      log('Remote Config force fetched successfully');
      return true;
    } catch (e) {
      log('Error force fetching remote config: $e');
      return false;
    }
  }

  /// Check if enhance prompt feature is enabled
  bool get isEnhancePromptFeatureEnabled {
    return _remoteConfig?.getBool(_enhancePromptFeatureKey) ?? 
           _defaultValues[_enhancePromptFeatureKey] as bool;
  }

  /// Check if generate image feature is enabled
  bool get isGenerateImageFeatureEnabled {
    return _remoteConfig?.getBool(_generateImageFeatureKey) ?? 
           _defaultValues[_generateImageFeatureKey] as bool;
  }

  /// Check if image generation tab is enabled
  bool get isImageGenerationTabEnabled {
    return _remoteConfig?.getBool(_imageGenerationTabKey) ?? 
           _defaultValues[_imageGenerationTabKey] as bool;
  }

  /// Check if prompt enhancement tab is enabled
  bool get isPromptEnhancementTabEnabled {
    return _remoteConfig?.getBool(_promptEnhancementTabKey) ?? 
           _defaultValues[_promptEnhancementTabKey] as bool;
  }

  /// Get all feature flags as a map
  Map<String, bool> get allFeatureFlags {
    return {
      'enhancePromptFeature': isEnhancePromptFeatureEnabled,
      'generateImageFeature': isGenerateImageFeatureEnabled,
      'imageGenerationTab': isImageGenerationTabEnabled,
      'promptEnhancementTab': isPromptEnhancementTabEnabled,
    };
  }

  /// Get a custom string value from remote config
  String getString(String key, {String defaultValue = ''}) {
    return _remoteConfig?.getString(key) ?? defaultValue;
  }

  /// Get a custom boolean value from remote config
  bool getBool(String key, {bool defaultValue = false}) {
    return _remoteConfig?.getBool(key) ?? defaultValue;
  }

  /// Get a custom integer value from remote config
  int getInt(String key, {int defaultValue = 0}) {
    return _remoteConfig?.getInt(key) ?? defaultValue;
  }

  /// Get a custom double value from remote config
  double getDouble(String key, {double defaultValue = 0.0}) {
    return _remoteConfig?.getDouble(key) ?? defaultValue;
  }

  /// Listen to remote config updates
  Stream<RemoteConfigUpdate> get onConfigUpdated {
    return _remoteConfig?.onConfigUpdated ?? const Stream.empty();
  }

  /// Get the last fetch time
  DateTime? get lastFetchTime {
    return _remoteConfig?.lastFetchTime;
  }

  /// Get the last fetch status
  RemoteConfigFetchStatus get lastFetchStatus {
    return _remoteConfig?.lastFetchStatus ?? RemoteConfigFetchStatus.noFetchYet;
  }

  /// Check if remote config is available
  bool get isAvailable {
    return _remoteConfig != null;
  }
}
